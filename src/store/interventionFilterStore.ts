import { create } from 'zustand';
import { GameActivity } from '../types/gameIntervention';

export interface FilterOptions {
  categories: string[];
  difficultyLevels: string[];
  targetAgeGroups: string[];
}

export interface InterventionFilters {
  searchQuery: string;
  selectedCategories: string[];
  selectedDifficultyLevels: string[];
  selectedTargetAgeGroups: string[];
}

interface InterventionFilterState {
  // Filter state
  filters: InterventionFilters;
  
  // Available filter options (extracted from data)
  availableOptions: FilterOptions;
  
  // Actions
  setSearchQuery: (query: string) => void;
  toggleCategory: (category: string) => void;
  toggleDifficultyLevel: (level: string) => void;
  toggleTargetAgeGroup: (ageGroup: string) => void;
  clearAllFilters: () => void;
  
  // Update available options based on intervention data
  updateAvailableOptions: (activities: GameActivity[]) => void;
  
  // Filter function
  filterActivities: (activities: GameActivity[]) => GameActivity[];
}

const initialFilters: InterventionFilters = {
  searchQuery: '',
  selectedCategories: [],
  selectedDifficultyLevels: [],
  selectedTargetAgeGroups: [],
};

const initialOptions: FilterOptions = {
  categories: [],
  difficultyLevels: [],
  targetAgeGroups: [],
};

export const useInterventionFilterStore = create<InterventionFilterState>((set, get) => ({
  filters: initialFilters,
  availableOptions: initialOptions,

  setSearchQuery: (query: string) => {
    set(state => ({
      filters: { ...state.filters, searchQuery: query }
    }));
  },

  toggleCategory: (category: string) => {
    set(state => {
      const selectedCategories = state.filters.selectedCategories.includes(category)
        ? state.filters.selectedCategories.filter(c => c !== category)
        : [...state.filters.selectedCategories, category];
      
      return {
        filters: { ...state.filters, selectedCategories }
      };
    });
  },

  toggleDifficultyLevel: (level: string) => {
    set(state => {
      const selectedDifficultyLevels = state.filters.selectedDifficultyLevels.includes(level)
        ? state.filters.selectedDifficultyLevels.filter(l => l !== level)
        : [...state.filters.selectedDifficultyLevels, level];
      
      return {
        filters: { ...state.filters, selectedDifficultyLevels }
      };
    });
  },

  toggleTargetAgeGroup: (ageGroup: string) => {
    set(state => {
      const selectedTargetAgeGroups = state.filters.selectedTargetAgeGroups.includes(ageGroup)
        ? state.filters.selectedTargetAgeGroups.filter(ag => ag !== ageGroup)
        : [...state.filters.selectedTargetAgeGroups, ageGroup];
      
      return {
        filters: { ...state.filters, selectedTargetAgeGroups }
      };
    });
  },

  clearAllFilters: () => {
    set({ filters: initialFilters });
  },

  updateAvailableOptions: (activities: GameActivity[]) => {
    const categories = new Set<string>();
    const difficultyLevels = new Set<string>();
    const targetAgeGroups = new Set<string>();

    activities.forEach(activity => {
      // Extract categories
      if (activity.categories && Array.isArray(activity.categories)) {
        activity.categories.forEach(category => categories.add(category));
      }

      // Extract difficulty levels
      if (activity.difficulty_level) {
        difficultyLevels.add(activity.difficulty_level);
      }

      // Extract target age groups
      if (activity.target_age_group) {
        targetAgeGroups.add(activity.target_age_group);
      }
    });

    set({
      availableOptions: {
        categories: Array.from(categories).sort(),
        difficultyLevels: Array.from(difficultyLevels).sort(),
        targetAgeGroups: Array.from(targetAgeGroups).sort(),
      }
    });
  },

  filterActivities: (activities: GameActivity[]): GameActivity[] => {
    const { filters } = get();
    
    return activities.filter(activity => {
      // Search query filter
      if (filters.searchQuery) {
        const searchLower = filters.searchQuery.toLowerCase();
        const titleMatch = activity.title?.toLowerCase().includes(searchLower);
        const descriptionMatch = activity.description?.toLowerCase().includes(searchLower);
        const objectiveMatch = activity.objective?.toLowerCase().includes(searchLower);
        
        if (!titleMatch && !descriptionMatch && !objectiveMatch) {
          return false;
        }
      }

      // Category filter
      if (filters.selectedCategories.length > 0) {
        const hasMatchingCategory = activity.categories?.some(category =>
          filters.selectedCategories.includes(category)
        );
        if (!hasMatchingCategory) {
          return false;
        }
      }

      // Difficulty level filter
      if (filters.selectedDifficultyLevels.length > 0) {
        if (!filters.selectedDifficultyLevels.includes(activity.difficulty_level)) {
          return false;
        }
      }

      // Target age group filter
      if (filters.selectedTargetAgeGroups.length > 0) {
        if (!filters.selectedTargetAgeGroups.includes(activity.target_age_group)) {
          return false;
        }
      }

      return true;
    });
  },
}));
