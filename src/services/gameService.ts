import gameImage from '../assets/game1.png';
import mathGameImage from '../assets/game2.png';
import memoryGameImage from '../assets/game3.png';
import logger from '../utils/logger';
import { testService } from './testService';
import { Test } from '../types/test';
import { api } from './api';
import { GameActivity } from '../types/gameIntervention';

export type GameType = 'blend-game--quiz' | 'math-game' | 'memory-game' | string;

export interface Game {
  id: GameType;
  name: string;
  description: string;
  thumbnail: string;
  categories?: string[];
  difficulty_level?: string;
  target_age_group?: string;
}

interface InterventionActivity {
  title: string;
  description: string;
  images: string[];
  numberOfExercises: number;
  game_id: string;
  objective: string;
  target_age_group: string;
  difficulty_level: string;
  categories: string[];
}

interface Intervention {
  id: number;
  title: string;
  activities: InterventionActivity[];
}

export interface GameResult {
  studentId: string;
  gameId: string;
  correctAnswers: number;
  totalQuestions: number;
  timeInSeconds: number;
  completedAt: string;
}

// Lokalna baza danych gier
const localGames: Record<string, Game> = {
  'blend-game--quiz': {
    id: 'blend-game--quiz',
    name: 'Blend Activity',
    description: 'Practice blending sounds with this interactive game',
    thumbnail: gameImage,
    categories: ['Letter Sounds', 'Long Vowels', 'Plural Endings', 'Multi Syllabic Words'],
    difficulty_level: 'medium',
    target_age_group: '5-8',
  },
  'math-game': {
    id: 'math-game',
    name: 'Math Quiz',
    description: 'Practice math skills with this interactive quiz',
    thumbnail: mathGameImage,
    categories: [
      'Phonics Awareness',
      'Blending and Segmenting Sounds',
      'Plural Endings',
      'Prefixes',
    ],
    difficulty_level: 'medium',
    target_age_group: '6-10',
  },
  'memory-game': {
    id: 'memory-game',
    name: 'Memory Game',
    description: 'Test your memory by matching pairs in this fun card game',
    thumbnail: memoryGameImage,
    categories: ['Phonics', 'Sight Words', 'Compound Words', 'Homophone and Homographs'],
    difficulty_level: 'easy',
    target_age_group: '4-7',
  },
  'mastermind-game': {
    id: 'mastermind-game',
    name: 'Number Code Breaker',
    description: 'Guess the secret number code based on hints',
    thumbnail: mathGameImage, // Temporarily using existing image
    categories: ['Math', 'Logic', 'Problem Solving'],
    difficulty_level: 'medium',
    target_age_group: '7-12',
  },
};

// We don't store game results locally - everything through WebSocket

export interface GameFilters {
  searchQuery?: string;
  selectedCategories?: string[];
  selectedDifficultyLevels?: string[];
  selectedTargetAgeGroups?: string[];
}

export const gameService = {
  getGames: async (isTestsPage?: boolean, filters?: GameFilters): Promise<{ games: Game[] }> => {
    try {
      // If this is the Tests tab, fetch assessments instead
      if (isTestsPage) {
        const tests = await testService.listTests();
        const transformedTests = tests.map((test: Test) => ({
          id: test.id,
          name: test.title,
          description: `Assessment with ${test.questions.length} questions`,
          thumbnail: gameImage,
          categories: ['Assessment'],
          difficulty_level: 'medium',
          target_age_group: '6-12',
        }));
        return { games: transformedTests };
      }

      const response = await api.get('games/all_interventions');
      const data = response.data;

      // Transform data from new API format to Game[] format
      if (!Array.isArray(data) || data.length === 0) {
        logger.warn('No intervention data from API');
        return { games: [] };
      }

      // Create games based on each intervention and its activities
      const transformedGames = data.flatMap((intervention: Intervention) => {
        if (
          !intervention.activities ||
          !Array.isArray(intervention.activities) ||
          intervention.activities.length === 0
        ) {
          logger.warn(`No activities for intervention: ${intervention.title || intervention.id}`);
          return [];
        }

        return intervention.activities.map((activity: InterventionActivity) => ({
          id: activity.game_id || `${intervention.id}-${activity.title}`,
          name: activity.title,
          description: activity.description,
          thumbnail: activity.images && activity.images.length > 0 ? activity.images[0] : gameImage,
          categories: activity.categories || [],
          difficulty_level: activity.difficulty_level,
          target_age_group: activity.target_age_group,
        }));
      });

      if (transformedGames.length === 0) {
        logger.warn('Failed to load any active interventions');
      } else {
        logger.info(`Retrieved ${transformedGames.length} intervention activities`);
      }

      // Apply filters if provided
      let filteredGames = transformedGames;
      if (filters) {
        filteredGames = applyGameFilters(transformedGames, filters);
      }

      return { games: filteredGames };
    } catch (error) {
      logger.error('Error while fetching interventions:', error);
      return { games: [] };
    }
  },

  getGame: async (id: string): Promise<Game> => {
    // Return game with given ID or default game
    return localGames[id] || localGames['blend-game--quiz'];
  },

  saveGameResult: async (result: Omit<GameResult, 'completedAt'>): Promise<GameResult> => {
    // Create result with timestamp, but don't save locally
    logger.log('[gameService] Preparing game result to send via WebSocket:', result);

    // Return result with added completedAt field
    const completedAt = new Date().toISOString();
    return { ...result, completedAt };
  },
};

// Helper function to apply filters to games
const applyGameFilters = (games: Game[], filters: GameFilters): Game[] => {
  return games.filter(game => {
    // Search query filter
    if (filters.searchQuery) {
      const searchLower = filters.searchQuery.toLowerCase();
      const nameMatch = game.name?.toLowerCase().includes(searchLower);
      const descriptionMatch = game.description?.toLowerCase().includes(searchLower);

      if (!nameMatch && !descriptionMatch) {
        return false;
      }
    }

    // Category filter
    if (filters.selectedCategories && filters.selectedCategories.length > 0) {
      const hasMatchingCategory = game.categories?.some(category =>
        filters.selectedCategories!.includes(category)
      );
      if (!hasMatchingCategory) {
        return false;
      }
    }

    // Difficulty level filter
    if (filters.selectedDifficultyLevels && filters.selectedDifficultyLevels.length > 0) {
      if (
        !game.difficulty_level ||
        !filters.selectedDifficultyLevels.includes(game.difficulty_level)
      ) {
        return false;
      }
    }

    // Target age group filter
    if (filters.selectedTargetAgeGroups && filters.selectedTargetAgeGroups.length > 0) {
      if (
        !game.target_age_group ||
        !filters.selectedTargetAgeGroups.includes(game.target_age_group)
      ) {
        return false;
      }
    }

    return true;
  });
};
